/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : K230D红色追踪EMMV5云台控制系统主程序
  * <AUTHOR> 米醋电子工作室技术团队
  * @version        : V1.0
  * @date           : 2025-07-29
  ******************************************************************************
  * @attention
  *
  * 功能描述：
  * 1. 接收K230D发送的红色目标坐标数据
  * 2. 通过PID算法控制EMMV5云台追踪红色目标
  * 3. 集成OLED显示、JY901S姿态传感器等功能
  * 4. 实现完整的视觉追踪云台系统
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "stm32f4xx_hal.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include <stdio.h>
#include <string.h>
#include <math.h>
#include "../Moudle/EMMV5_PID/EMMV5_PID.h"
#include "../Moudle/K230D_getData/k230d_getdata.h"
#include "../Moudle/jy901s/jy901s.h"
#include "../Moudle/oled/oled.h"

/* printf重定向到UART1 (调试串口) */
#ifdef __GNUC__
#define PUTCHAR_PROTOTYPE int __io_putchar(int ch)
#else
#define PUTCHAR_PROTOTYPE int fputc(int ch, FILE *f)
#endif

PUTCHAR_PROTOTYPE
{
  /* 这里可以重定向到任何UART，暂时注释掉避免编译错误 */
  /* HAL_UART_Transmit(&huart1, (uint8_t *)&ch, 1, 0xFFFF); */
  return ch;
}
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* UART句柄 */
UART_HandleTypeDef huart2;
UART_HandleTypeDef huart4;
UART_HandleTypeDef huart5;
UART_HandleTypeDef huart6;

/* USER CODE BEGIN PV */

/* 系统核心实例 */
EMMV5_PID_System_t *g_emmv5_pid_system = NULL;
JY901S_Handle g_jy901s_handle;

/* UART接收缓冲区 */
uint8_t uart_rx_buffer[1];

/* 系统状态变量 */
uint32_t system_tick_counter = 0;
uint8_t system_status = 0;  // 0=初始化, 1=运行中, 2=追踪中, 3=错误

/* 追踪系统控制变量 */
uint32_t last_control_time = 0;
uint32_t last_display_time = 0;
uint32_t control_period = 50;  // 20Hz控制频率
bool tracking_enabled = false;
bool debug_mode = true;

/* 追踪统计变量 */
uint32_t tracking_start_time = 0;
uint32_t total_tracking_time = 0;
uint32_t valid_coordinates_count = 0;
uint32_t lost_target_count = 0;

/* 显示缓冲区 */
char display_buffer[32];

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
static void MX_GPIO_Init(void);
static void MX_DMA_Init(void);
static void MX_I2C1_Init(void);
static void MX_I2C2_Init(void);
static void MX_TIM1_Init(void);
static void MX_UART4_Init(void);
static void MX_UART5_Init(void);
static void MX_USART2_UART_Init(void);
static void MX_USART6_UART_Init(void);
/* USER CODE BEGIN PFP */

/* 系统功能函数声明 */
void System_Init(void);
void System_MainLoop(void);
void System_DisplayStatus(void);
void System_ErrorHandler(void);
void System_PeriodicTasks(void);

/* K230D追踪系统函数声明 */
void K230D_Tracking_Init(void);
void K230D_Tracking_Process(void);
void K230D_Tracking_UpdateDisplay(void);
void K230D_Tracking_EnableTracking(bool enable);
bool K230D_Tracking_IsTargetValid(void);
void K230D_Tracking_ResetStats(void);

/* UART中断回调函数 */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart);

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/**
 * @brief 系统初始化
 */
void System_Init(void)
{
    printf("\r\n=== K230D红色追踪EMMV5云台控制系统 ===\r\n");
    printf("版本: V1.0\r\n");
    printf("作者: 米醋电子工作室\r\n");
    printf("日期: 2025-07-29\r\n");
    printf("=====================================\r\n");

    /* 1. OLED显示器初始化 */
    OLED_Init();
    OLED_Clear();
    OLED_ShowStr(0, 0, "K230D Tracking", 16);
    OLED_ShowStr(0, 2, "System Init...", 12);
    printf("OLED显示器初始化完成\r\n");

    /* 2. K230D数据接收模块初始化 */
    if (K230D_Init(&huart6) == HAL_OK) {
        printf("K230D数据接收模块初始化完成\r\n");
    } else {
        printf("K230D数据接收模块初始化失败\r\n");
        System_ErrorHandler();
    }

    /* 3. JY901S姿态传感器初始化 (暂时不启用) */
    // JY901S_Init(&g_jy901s_handle, &huart5, NULL);
    // HAL_UART_Receive_IT(&huart5, uart_rx_buffer, 1);
    // printf("JY901S姿态传感器初始化完成\r\n");
    printf("JY901S姿态传感器暂时未启用\r\n");

    /* 4. EMMV5 PID云台追踪系统初始化 */
    K230D_Tracking_Init();
    printf("EMMV5 PID云台追踪系统初始化完成\r\n");

    /* 5. 显示初始化完成信息 */
    OLED_Clear();
    OLED_ShowStr(0, 0, "System Ready!", 16);
    OLED_ShowStr(0, 2, "Tracking: OFF", 12);
    OLED_ShowStr(0, 4, "Target: NONE", 12);

    system_status = 1;  // 系统运行状态
    printf("系统初始化完成，开始运行主循环\r\n");

    HAL_Delay(2000);  // 显示2秒初始化信息
}

/**
 * @brief 系统主循环
 */
void System_MainLoop(void)
{
    /* K230D数据处理任务 */
    K230D_ProcessData();

    /* K230D追踪控制任务 */
    K230D_Tracking_Process();

    /* JY901S数据处理任务 (暂时不启用) */
    // JY901S_ProcessUARTData(&g_jy901s_handle);

    /* 周期性任务 */
    System_PeriodicTasks();
}

/**
 * @brief 周期性任务处理
 */
void System_PeriodicTasks(void)
{
    uint32_t current_time = HAL_GetTick();
    
    /* 每500ms更新一次显示 */
    if (current_time - last_display_time >= 500) {
        System_DisplayStatus();
        last_display_time = current_time;
    }
    
    /* 系统心跳计数 */
    system_tick_counter++;
    
    /* 每10秒打印一次系统状态 */
    if (system_tick_counter % 10000 == 0) {
        printf("系统运行正常，心跳计数: %lu\r\n", system_tick_counter);

        /* 打印K230D追踪系统统计信息 */
        if (g_emmv5_pid_system != NULL) {
            printf("追踪统计 - 有效坐标:%lu, 丢失目标:%lu, 追踪状态:%s\r\n",
                   valid_coordinates_count, lost_target_count,
                   tracking_enabled ? "启用" : "禁用");

            /* 打印K230D数据统计 */
            K230D_Data_t *k230d_stats = K230D_GetStats();
            if (k230d_stats != NULL) {
                printf("K230D统计 - 总字节:%lu, 有效包:%lu, 数据年龄:%lums\r\n",
                       k230d_stats->total_bytes, k230d_stats->valid_packets,
                       K230D_GetDataAge());
            }
        }
    }
}

/**
 * @brief 系统状态显示
 */
void System_DisplayStatus(void)
{
    /* 使用新的追踪系统显示函数 */
    K230D_Tracking_UpdateDisplay();
}

/**
 * @brief 系统错误处理
 */
void System_ErrorHandler(void)
{
    system_status = 2;  // 错误状态
    
    OLED_Clear();
    OLED_ShowStr(0, 0, "SYSTEM ERROR!", 16);
    OLED_ShowStr(0, 2, "Check Hardware", 12);
    
    printf("系统发生错误，请检查硬件连接\r\n");
    
    /* 停止云台电机 */
    if (g_emmv5_system != NULL) {
        EMMV5_Simple_StopMotors(g_emmv5_system);
        EMMV5_Simple_Enable(g_emmv5_system, false);
    }
    
    /* 错误指示灯闪烁 */
    while (1) {
        HAL_Delay(500);
        // 这里可以添加LED闪烁代码
    }
}

/**
 * @brief UART接收完成回调函数
 */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    /* K230D数据接收处理 - 使用DMA方式，此回调函数暂时不需要处理 */
    /* 数据处理在主循环中通过K230D_ProcessData()完成 */

    /* JY901S数据接收处理 (暂时不启用) */
    /*
    if (huart->Instance == UART5) {
        JY901S_ProcessByte(&g_jy901s_handle, uart_rx_buffer[0]);
        HAL_UART_Receive_IT(&huart5, uart_rx_buffer, 1);
    }
    */
}

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{
  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_I2C1_Init();
  MX_I2C2_Init();
  MX_TIM1_Init();
  MX_UART4_Init();
  MX_UART5_Init();
  MX_USART2_UART_Init();
  MX_USART6_UART_Init();
  /* USER CODE BEGIN 2 */

  /* 系统初始化 */
  System_Init();

  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* 系统主循环 */
    System_MainLoop();
    
    /* 短暂延时，避免CPU占用过高 */
    HAL_Delay(1);

    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_RCC_PWR_CLK_ENABLE();
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSI;
  RCC_OscInitStruct.PLL.PLLM = 8;
  RCC_OscInitStruct.PLL.PLLN = 180;
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;
  RCC_OscInitStruct.PLL.PLLQ = 4;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Activate the Over-Drive mode
  */
  if (HAL_PWREx_EnableOverDrive() != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV4;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV2;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_5) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

/**
 * @brief GPIO初始化
 */
static void MX_GPIO_Init(void)
{
  /* GPIO Ports Clock Enable */
  __HAL_RCC_GPIOA_CLK_ENABLE();
  __HAL_RCC_GPIOB_CLK_ENABLE();
  __HAL_RCC_GPIOC_CLK_ENABLE();
  __HAL_RCC_GPIOD_CLK_ENABLE();
  __HAL_RCC_GPIOE_CLK_ENABLE();
}

/**
 * @brief DMA初始化
 */
static void MX_DMA_Init(void)
{
  /* DMA controller clock enable */
  __HAL_RCC_DMA1_CLK_ENABLE();
  __HAL_RCC_DMA2_CLK_ENABLE();
}

/**
 * @brief I2C1初始化
 */
static void MX_I2C1_Init(void)
{
  /* I2C1 clock enable */
  __HAL_RCC_I2C1_CLK_ENABLE();
}

/**
 * @brief I2C2初始化
 */
static void MX_I2C2_Init(void)
{
  /* I2C2 clock enable */
  __HAL_RCC_I2C2_CLK_ENABLE();
}

/**
 * @brief TIM1初始化
 */
static void MX_TIM1_Init(void)
{
  /* TIM1 clock enable */
  __HAL_RCC_TIM1_CLK_ENABLE();
}

/**
 * @brief UART4初始化 (EMMV5 Y轴电机)
 */
static void MX_UART4_Init(void)
{
  huart4.Instance = UART4;
  huart4.Init.BaudRate = 9600;
  huart4.Init.WordLength = UART_WORDLENGTH_8B;
  huart4.Init.StopBits = UART_STOPBITS_1;
  huart4.Init.Parity = UART_PARITY_NONE;
  huart4.Init.Mode = UART_MODE_TX_RX;
  huart4.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart4.Init.OverSampling = UART_OVERSAMPLING_16;
  if (HAL_UART_Init(&huart4) != HAL_OK) {
    Error_Handler();
  }
}

/**
 * @brief UART5初始化 (JY901S姿态传感器)
 */
static void MX_UART5_Init(void)
{
  huart5.Instance = UART5;
  huart5.Init.BaudRate = 9600;
  huart5.Init.WordLength = UART_WORDLENGTH_8B;
  huart5.Init.StopBits = UART_STOPBITS_1;
  huart5.Init.Parity = UART_PARITY_NONE;
  huart5.Init.Mode = UART_MODE_TX_RX;
  huart5.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart5.Init.OverSampling = UART_OVERSAMPLING_16;
  if (HAL_UART_Init(&huart5) != HAL_OK) {
    Error_Handler();
  }
}

/**
 * @brief USART2初始化 (EMMV5 X轴电机)
 */
static void MX_USART2_UART_Init(void)
{
  huart2.Instance = USART2;
  huart2.Init.BaudRate = 9600;
  huart2.Init.WordLength = UART_WORDLENGTH_8B;
  huart2.Init.StopBits = UART_STOPBITS_1;
  huart2.Init.Parity = UART_PARITY_NONE;
  huart2.Init.Mode = UART_MODE_TX_RX;
  huart2.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart2.Init.OverSampling = UART_OVERSAMPLING_16;
  if (HAL_UART_Init(&huart2) != HAL_OK) {
    Error_Handler();
  }
}

/**
 * @brief USART6初始化 (K230D数据接收)
 */
static void MX_USART6_UART_Init(void)
{
  huart6.Instance = USART6;
  huart6.Init.BaudRate = 115200;
  huart6.Init.WordLength = UART_WORDLENGTH_8B;
  huart6.Init.StopBits = UART_STOPBITS_1;
  huart6.Init.Parity = UART_PARITY_NONE;
  huart6.Init.Mode = UART_MODE_TX_RX;
  huart6.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart6.Init.OverSampling = UART_OVERSAMPLING_16;
  if (HAL_UART_Init(&huart6) != HAL_OK) {
    Error_Handler();
  }
}

/**
 * @brief UART MSP初始化
 */
void HAL_UART_MspInit(UART_HandleTypeDef* uartHandle)
{
  GPIO_InitTypeDef GPIO_InitStruct = {0};

  if(uartHandle->Instance==UART4)
  {
    /* UART4 clock enable */
    __HAL_RCC_UART4_CLK_ENABLE();
    __HAL_RCC_GPIOC_CLK_ENABLE();

    /* UART4 GPIO Configuration: PC10->TX, PC11->RX */
    GPIO_InitStruct.Pin = GPIO_PIN_10|GPIO_PIN_11;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF8_UART4;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
  }
  else if(uartHandle->Instance==UART5)
  {
    /* UART5 clock enable */
    __HAL_RCC_UART5_CLK_ENABLE();
    __HAL_RCC_GPIOC_CLK_ENABLE();
    __HAL_RCC_GPIOD_CLK_ENABLE();

    /* UART5 GPIO Configuration: PC12->TX, PD2->RX */
    GPIO_InitStruct.Pin = GPIO_PIN_12;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF8_UART5;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);

    GPIO_InitStruct.Pin = GPIO_PIN_2;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF8_UART5;
    HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);
  }
  else if(uartHandle->Instance==USART2)
  {
    /* USART2 clock enable */
    __HAL_RCC_USART2_CLK_ENABLE();
    __HAL_RCC_GPIOA_CLK_ENABLE();

    /* USART2 GPIO Configuration: PA2->TX, PA3->RX */
    GPIO_InitStruct.Pin = GPIO_PIN_2|GPIO_PIN_3;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF7_USART2;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
  }
  else if(uartHandle->Instance==USART6)
  {
    /* USART6 clock enable */
    __HAL_RCC_USART6_CLK_ENABLE();
    __HAL_RCC_GPIOC_CLK_ENABLE();

    /* USART6 GPIO Configuration: PC6->TX, PC7->RX */
    GPIO_InitStruct.Pin = GPIO_PIN_6|GPIO_PIN_7;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF8_USART6;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
  }
}

/**
 * @brief 错误处理函数
 */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  System_ErrorHandler();
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  printf("Assert failed: file %s on line %lu\r\n", file, line);
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */

/**
 * @brief K230D追踪系统初始化
 */
void K230D_Tracking_Init(void)
{
    /* 获取EMMV5 PID系统实例 */
    g_emmv5_pid_system = EMMV5_PID_GetSystemInstance();

    /* 初始化EMMV5 PID系统 */
    EMMV5_PID_Init(g_emmv5_pid_system, NULL, &huart2, &huart4);

    /* 配置PID参数 */
    EMMV5_PID_Config(g_emmv5_pid_system,
                     2.0f, 0.1f, 0.5f,  // X轴PID参数
                     2.0f, 0.1f, 0.5f); // Y轴PID参数

    /* 使能电机 */
    EMMV5_PID_EnableMotors(g_emmv5_pid_system, true);

    /* 设置调试模式 */
    g_emmv5_pid_system->debug_mode = debug_mode;

    /* 初始化控制时间 */
    last_control_time = HAL_GetTick();

    printf("K230D追踪系统初始化完成\r\n");
    printf("PID参数: Kp=2.0, Ki=0.1, Kd=0.5\r\n");
    printf("控制频率: %luHz\r\n", 1000 / control_period);
}

/**
 * @brief K230D追踪处理主函数
 */
void K230D_Tracking_Process(void)
{
    uint32_t current_time = HAL_GetTick();
    int16_t x, y;

    /* 控制频率限制 (20Hz) */
    if (current_time - last_control_time < control_period) {
        return;
    }
    last_control_time = current_time;

    /* 获取K230D坐标数据 */
    if (K230D_GetCoordinates(&x, &y)) {
        /* 检查数据有效性 */
        if (x >= 0 && x <= 640 && y >= 0 && y <= 480) {
            /* 设置追踪目标 */
            EMMV5_PID_SetTarget(g_emmv5_pid_system, x, y);

            /* 启用追踪 */
            if (!tracking_enabled) {
                K230D_Tracking_EnableTracking(true);
            }

            /* 更新统计 */
            valid_coordinates_count++;

            /* 更新系统状态 */
            system_status = 2;  // 追踪中

            if (debug_mode) {
                printf("Target: X=%d, Y=%d\r\n", x, y);
            }
        } else {
            /* 坐标越界，忽略此次数据 */
            if (debug_mode) {
                printf("Invalid coordinates: X=%d, Y=%d\r\n", x, y);
            }
        }

        /* 清除数据更新标志 */
        K230D_ClearUpdateFlag();
    } else {
        /* 检查数据超时 */
        uint32_t data_age = K230D_GetDataAge();
        if (data_age > 200 && tracking_enabled) {
            /* 数据超时，停止追踪 */
            K230D_Tracking_EnableTracking(false);
            lost_target_count++;

            if (debug_mode) {
                printf("Target lost, data age: %lums\r\n", data_age);
            }
        }
    }

    /* 执行PID控制更新 */
    if (tracking_enabled) {
        EMMV5_PID_UpdateControl(g_emmv5_pid_system);
    }
}

/**
 * @brief 启用/禁用追踪功能
 */
void K230D_Tracking_EnableTracking(bool enable)
{
    tracking_enabled = enable;

    if (enable) {
        /* 启用追踪 */
        g_emmv5_pid_system->auto_tracking = true;
        g_emmv5_pid_system->gimbal.tracking_active = true;
        tracking_start_time = HAL_GetTick();
        printf("追踪已启用\r\n");
    } else {
        /* 禁用追踪，停止所有电机 */
        g_emmv5_pid_system->auto_tracking = false;
        g_emmv5_pid_system->gimbal.tracking_active = false;
        EMMV5_PID_StopMotors(g_emmv5_pid_system);

        /* 更新统计 */
        if (tracking_start_time > 0) {
            total_tracking_time += HAL_GetTick() - tracking_start_time;
        }

        system_status = 1;  // 运行中但未追踪
        printf("追踪已禁用\r\n");
    }
}

/**
 * @brief 检查目标是否有效
 */
bool K230D_Tracking_IsTargetValid(void)
{
    return (K230D_GetDataAge() <= 200) && tracking_enabled;
}

/**
 * @brief 重置追踪统计
 */
void K230D_Tracking_ResetStats(void)
{
    valid_coordinates_count = 0;
    lost_target_count = 0;
    total_tracking_time = 0;
    tracking_start_time = 0;

    /* 重置K230D统计 */
    K230D_ResetStats();

    printf("追踪统计已重置\r\n");
}

/**
 * @brief 更新追踪系统显示
 */
void K230D_Tracking_UpdateDisplay(void)
{
    OLED_Clear();

    /* 第1行：系统标题 */
    OLED_ShowStr(0, 0, "K230D Tracking", 16);

    /* 第2行：追踪状态 */
    if (tracking_enabled && K230D_Tracking_IsTargetValid()) {
        OLED_ShowStr(0, 2, "Status: ACTIVE", 12);
    } else if (tracking_enabled) {
        OLED_ShowStr(0, 2, "Status: SEARCH", 12);
    } else {
        OLED_ShowStr(0, 2, "Status: IDLE", 12);
    }

    /* 第3行：目标坐标 */
    if (g_emmv5_pid_system != NULL && g_emmv5_pid_system->target.valid) {
        snprintf(display_buffer, sizeof(display_buffer), "X:%d Y:%d",
                g_emmv5_pid_system->target.x, g_emmv5_pid_system->target.y);
        OLED_ShowStr(0, 4, display_buffer, 12);
    } else {
        OLED_ShowStr(0, 4, "Target: NONE", 12);
    }

    /* 第4行：统计信息 */
    snprintf(display_buffer, sizeof(display_buffer), "Valid:%lu Lost:%lu",
            valid_coordinates_count, lost_target_count);
    OLED_ShowStr(0, 6, display_buffer, 12);
}

/* USER CODE END 4 */
