/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : K230D红色追踪EMMV5云台控制系统主程序
  * <AUTHOR> 米醋电子工作室技术团队
  * @version        : V1.0
  * @date           : 2025-07-29
  ******************************************************************************
  * @attention
  *
  * 功能描述：
  * 1. 接收K230D发送的红色目标坐标数据
  * 2. 通过PID算法控制EMMV5云台追踪红色目标
  * 3. 集成OLED显示、JY901S姿态传感器等功能
  * 4. 实现完整的视觉追踪云台系统
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "stm32f4xx_hal.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include <stdio.h>
#include <string.h>
#include "../Moudle/EMMV5_Simple/emmv5_simple.h"
#include "../Moudle/K230D_getData/k230d_getdata.h"
#include "../Moudle/jy901s/jy901s.h"
#include "../Moudle/oled/oled.h"

/* printf重定向到UART1 (调试串口) */
#ifdef __GNUC__
#define PUTCHAR_PROTOTYPE int __io_putchar(int ch)
#else
#define PUTCHAR_PROTOTYPE int fputc(int ch, FILE *f)
#endif

PUTCHAR_PROTOTYPE
{
  /* 这里可以重定向到任何UART，暂时注释掉避免编译错误 */
  /* HAL_UART_Transmit(&huart1, (uint8_t *)&ch, 1, 0xFFFF); */
  return ch;
}
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* UART句柄 */
UART_HandleTypeDef huart2;
UART_HandleTypeDef huart4;
UART_HandleTypeDef huart5;
UART_HandleTypeDef huart6;

/* USER CODE BEGIN PV */

/* 系统核心实例 */
EMMV5_Simple_t *g_emmv5_system = NULL;
K230D_Handle g_k230d_handle;
JY901S_Handle g_jy901s_handle;

/* UART接收缓冲区 */
uint8_t uart_rx_buffer[1];
uint8_t k230d_rx_buffer[1];

/* 系统状态变量 */
uint32_t system_tick_counter = 0;
uint8_t system_status = 0;  // 0=初始化, 1=运行中, 2=错误

/* 调试和统计变量 */
uint32_t debug_counter = 0;
uint32_t last_display_time = 0;

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
static void MX_GPIO_Init(void);
static void MX_DMA_Init(void);
static void MX_I2C1_Init(void);
static void MX_I2C2_Init(void);
static void MX_TIM1_Init(void);
static void MX_UART4_Init(void);
static void MX_UART5_Init(void);
static void MX_USART2_UART_Init(void);
static void MX_USART6_UART_Init(void);
/* USER CODE BEGIN PFP */

/* 系统功能函数声明 */
void System_Init(void);
void System_MainLoop(void);
void System_DisplayStatus(void);
void System_ErrorHandler(void);
void System_PeriodicTasks(void);

/* UART中断回调函数 */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart);

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/**
 * @brief 系统初始化
 */
void System_Init(void)
{
    printf("\r\n=== K230D红色追踪EMMV5云台控制系统 ===\r\n");
    printf("版本: V1.0\r\n");
    printf("作者: 米醋电子工作室\r\n");
    printf("日期: 2025-07-29\r\n");
    printf("=====================================\r\n");
    
    /* 1. OLED显示器初始化 */
    OLED_Init();
    OLED_Clear();
    OLED_ShowStr(0, 0, "K230D Tracking", 16);
    OLED_ShowStr(0, 2, "System Init...", 12);
    printf("OLED显示器初始化完成\r\n");
    
    /* 2. K230D数据接收模块初始化 */
    K230D_Init(&g_k230d_handle, &huart6);
    HAL_UART_Receive_IT(&huart6, k230d_rx_buffer, 1);
    printf("K230D数据接收模块初始化完成\r\n");

    /* 3. JY901S姿态传感器初始化 (暂时不启用) */
    // JY901S_Init(&g_jy901s_handle, &huart5, NULL);
    // HAL_UART_Receive_IT(&huart5, uart_rx_buffer, 1);
    // printf("JY901S姿态传感器初始化完成\r\n");
    printf("JY901S姿态传感器暂时未启用\r\n");

    /* 4. EMMV5轻量级云台系统初始化 */
    g_emmv5_system = EMMV5_Simple_GetInstance();
    EMMV5_Simple_Init(g_emmv5_system, &huart2, &huart4);
    printf("EMMV5轻量级云台系统初始化完成\r\n");

    /* 5. 启用云台系统 */
    EMMV5_Simple_Enable(g_emmv5_system, true);
    EMMV5_Simple_SetDebug(g_emmv5_system, true);  /* 启用调试模式 */
    printf("云台系统已启用\r\n");

    /* 6. 显示初始化完成信息 */
    OLED_Clear();
    OLED_ShowStr(0, 0, "System Ready!", 16);
    OLED_ShowStr(0, 2, "Tracking: OFF", 12);
    OLED_ShowStr(0, 4, "Target: NONE", 12);
    
    system_status = 1;  // 系统运行状态
    printf("系统初始化完成，开始运行主循环\r\n");
    
    HAL_Delay(2000);  // 显示2秒初始化信息
}

/**
 * @brief 系统主循环
 */
void System_MainLoop(void)
{
    /* EMMV5轻量级控制任务 */
    EMMV5_Simple_Task(g_emmv5_system);

    /* K230D数据处理任务 */
    K230D_ProcessUARTData(&g_k230d_handle);

    /* JY901S数据处理任务 (暂时不启用) */
    // JY901S_ProcessUARTData(&g_jy901s_handle);

    /* 周期性任务 */
    System_PeriodicTasks();
}

/**
 * @brief 周期性任务处理
 */
void System_PeriodicTasks(void)
{
    uint32_t current_time = HAL_GetTick();
    
    /* 每500ms更新一次显示 */
    if (current_time - last_display_time >= 500) {
        System_DisplayStatus();
        last_display_time = current_time;
    }
    
    /* 系统心跳计数 */
    system_tick_counter++;
    
    /* 每10秒打印一次系统状态 */
    if (system_tick_counter % 10000 == 0) {
        printf("系统运行正常，心跳计数: %lu\r\n", system_tick_counter);
        
        /* 打印EMMV5系统统计信息 */
        if (g_emmv5_system != NULL) {
            printf("EMMV5统计 - 坐标:%lu, 控制周期:%lu, FPS:%.1f\r\n",
                   g_emmv5_system->stats.valid_coords,
                   g_emmv5_system->stats.control_cycles,
                   EMMV5_Simple_GetFPS(g_emmv5_system));
        }
    }
}

/**
 * @brief 系统状态显示
 */
void System_DisplayStatus(void)
{
    OLED_Clear();
    
    /* 第1行：系统标题 */
    OLED_ShowStr(0, 0, "K230D Tracking", 16);
    
    /* 第2行：追踪状态 */
    if (g_emmv5_system != NULL && EMMV5_Simple_IsTracking(g_emmv5_system)) {
        OLED_ShowStr(0, 2, "Status: ACTIVE", 12);

        /* 第3行：目标坐标 */
        char coord_str[32];
        snprintf(coord_str, sizeof(coord_str), "X:%d Y:%d",
                g_emmv5_system->target.x, g_emmv5_system->target.y);
        OLED_ShowStr(0, 4, coord_str, 12);

        /* 第4行：电机速度 */
        char speed_str[32];
        snprintf(speed_str, sizeof(speed_str), "Spd:%d,%d",
                g_emmv5_system->gimbal.x_speed, g_emmv5_system->gimbal.y_speed);
        OLED_ShowStr(0, 6, speed_str, 8);
    } else {
        OLED_ShowStr(0, 2, "Status: SEARCH", 12);
        OLED_ShowStr(0, 4, "Target: NONE", 12);
        OLED_ShowStr(0, 6, "Waiting...", 8);
    }
}

/**
 * @brief 系统错误处理
 */
void System_ErrorHandler(void)
{
    system_status = 2;  // 错误状态
    
    OLED_Clear();
    OLED_ShowStr(0, 0, "SYSTEM ERROR!", 16);
    OLED_ShowStr(0, 2, "Check Hardware", 12);
    
    printf("系统发生错误，请检查硬件连接\r\n");
    
    /* 停止云台电机 */
    if (g_emmv5_system != NULL) {
        EMMV5_Simple_StopMotors(g_emmv5_system);
        EMMV5_Simple_Enable(g_emmv5_system, false);
    }
    
    /* 错误指示灯闪烁 */
    while (1) {
        HAL_Delay(500);
        // 这里可以添加LED闪烁代码
    }
}

/**
 * @brief UART接收完成回调函数
 */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == USART6) {
        /* K230D数据接收处理 */
        K230D_ProcessByte(&g_k230d_handle, k230d_rx_buffer[0]);

        /* 如果接收到完整数据包，更新EMMV5目标 */
        if (g_k230d_handle.data_ready) {
            if (g_emmv5_system != NULL) {
                EMMV5_Simple_SetTarget(g_emmv5_system,
                                      g_k230d_handle.coordinate.x,
                                      g_k230d_handle.coordinate.y);
            }
            g_k230d_handle.data_ready = false;
        }

        /* 重新启动接收 */
        HAL_UART_Receive_IT(&huart6, k230d_rx_buffer, 1);
    }
    /* JY901S数据接收处理 (暂时不启用) */
    /*
    else if (huart->Instance == UART5) {
        JY901S_ProcessByte(&g_jy901s_handle, uart_rx_buffer[0]);
        HAL_UART_Receive_IT(&huart5, uart_rx_buffer, 1);
    }
    */
}

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{
  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_I2C1_Init();
  MX_I2C2_Init();
  MX_TIM1_Init();
  MX_UART4_Init();
  MX_UART5_Init();
  MX_USART2_UART_Init();
  MX_USART6_UART_Init();
  /* USER CODE BEGIN 2 */

  /* 系统初始化 */
  System_Init();

  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* 系统主循环 */
    System_MainLoop();
    
    /* 短暂延时，避免CPU占用过高 */
    HAL_Delay(1);

    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_RCC_PWR_CLK_ENABLE();
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSI;
  RCC_OscInitStruct.PLL.PLLM = 8;
  RCC_OscInitStruct.PLL.PLLN = 180;
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;
  RCC_OscInitStruct.PLL.PLLQ = 4;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Activate the Over-Drive mode
  */
  if (HAL_PWREx_EnableOverDrive() != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV4;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV2;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_5) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

/**
 * @brief GPIO初始化
 */
static void MX_GPIO_Init(void)
{
  /* GPIO Ports Clock Enable */
  __HAL_RCC_GPIOA_CLK_ENABLE();
  __HAL_RCC_GPIOB_CLK_ENABLE();
  __HAL_RCC_GPIOC_CLK_ENABLE();
  __HAL_RCC_GPIOD_CLK_ENABLE();
  __HAL_RCC_GPIOE_CLK_ENABLE();
}

/**
 * @brief DMA初始化
 */
static void MX_DMA_Init(void)
{
  /* DMA controller clock enable */
  __HAL_RCC_DMA1_CLK_ENABLE();
  __HAL_RCC_DMA2_CLK_ENABLE();
}

/**
 * @brief I2C1初始化
 */
static void MX_I2C1_Init(void)
{
  /* I2C1 clock enable */
  __HAL_RCC_I2C1_CLK_ENABLE();
}

/**
 * @brief I2C2初始化
 */
static void MX_I2C2_Init(void)
{
  /* I2C2 clock enable */
  __HAL_RCC_I2C2_CLK_ENABLE();
}

/**
 * @brief TIM1初始化
 */
static void MX_TIM1_Init(void)
{
  /* TIM1 clock enable */
  __HAL_RCC_TIM1_CLK_ENABLE();
}

/**
 * @brief UART4初始化 (EMMV5 Y轴电机)
 */
static void MX_UART4_Init(void)
{
  huart4.Instance = UART4;
  huart4.Init.BaudRate = 9600;
  huart4.Init.WordLength = UART_WORDLENGTH_8B;
  huart4.Init.StopBits = UART_STOPBITS_1;
  huart4.Init.Parity = UART_PARITY_NONE;
  huart4.Init.Mode = UART_MODE_TX_RX;
  huart4.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart4.Init.OverSampling = UART_OVERSAMPLING_16;
  if (HAL_UART_Init(&huart4) != HAL_OK) {
    Error_Handler();
  }
}

/**
 * @brief UART5初始化 (JY901S姿态传感器)
 */
static void MX_UART5_Init(void)
{
  huart5.Instance = UART5;
  huart5.Init.BaudRate = 9600;
  huart5.Init.WordLength = UART_WORDLENGTH_8B;
  huart5.Init.StopBits = UART_STOPBITS_1;
  huart5.Init.Parity = UART_PARITY_NONE;
  huart5.Init.Mode = UART_MODE_TX_RX;
  huart5.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart5.Init.OverSampling = UART_OVERSAMPLING_16;
  if (HAL_UART_Init(&huart5) != HAL_OK) {
    Error_Handler();
  }
}

/**
 * @brief USART2初始化 (EMMV5 X轴电机)
 */
static void MX_USART2_UART_Init(void)
{
  huart2.Instance = USART2;
  huart2.Init.BaudRate = 9600;
  huart2.Init.WordLength = UART_WORDLENGTH_8B;
  huart2.Init.StopBits = UART_STOPBITS_1;
  huart2.Init.Parity = UART_PARITY_NONE;
  huart2.Init.Mode = UART_MODE_TX_RX;
  huart2.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart2.Init.OverSampling = UART_OVERSAMPLING_16;
  if (HAL_UART_Init(&huart2) != HAL_OK) {
    Error_Handler();
  }
}

/**
 * @brief USART6初始化 (K230D数据接收)
 */
static void MX_USART6_UART_Init(void)
{
  huart6.Instance = USART6;
  huart6.Init.BaudRate = 115200;
  huart6.Init.WordLength = UART_WORDLENGTH_8B;
  huart6.Init.StopBits = UART_STOPBITS_1;
  huart6.Init.Parity = UART_PARITY_NONE;
  huart6.Init.Mode = UART_MODE_TX_RX;
  huart6.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart6.Init.OverSampling = UART_OVERSAMPLING_16;
  if (HAL_UART_Init(&huart6) != HAL_OK) {
    Error_Handler();
  }
}

/**
 * @brief UART MSP初始化
 */
void HAL_UART_MspInit(UART_HandleTypeDef* uartHandle)
{
  GPIO_InitTypeDef GPIO_InitStruct = {0};

  if(uartHandle->Instance==UART4)
  {
    /* UART4 clock enable */
    __HAL_RCC_UART4_CLK_ENABLE();
    __HAL_RCC_GPIOC_CLK_ENABLE();

    /* UART4 GPIO Configuration: PC10->TX, PC11->RX */
    GPIO_InitStruct.Pin = GPIO_PIN_10|GPIO_PIN_11;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF8_UART4;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
  }
  else if(uartHandle->Instance==UART5)
  {
    /* UART5 clock enable */
    __HAL_RCC_UART5_CLK_ENABLE();
    __HAL_RCC_GPIOC_CLK_ENABLE();
    __HAL_RCC_GPIOD_CLK_ENABLE();

    /* UART5 GPIO Configuration: PC12->TX, PD2->RX */
    GPIO_InitStruct.Pin = GPIO_PIN_12;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF8_UART5;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);

    GPIO_InitStruct.Pin = GPIO_PIN_2;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF8_UART5;
    HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);
  }
  else if(uartHandle->Instance==USART2)
  {
    /* USART2 clock enable */
    __HAL_RCC_USART2_CLK_ENABLE();
    __HAL_RCC_GPIOA_CLK_ENABLE();

    /* USART2 GPIO Configuration: PA2->TX, PA3->RX */
    GPIO_InitStruct.Pin = GPIO_PIN_2|GPIO_PIN_3;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF7_USART2;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
  }
  else if(uartHandle->Instance==USART6)
  {
    /* USART6 clock enable */
    __HAL_RCC_USART6_CLK_ENABLE();
    __HAL_RCC_GPIOC_CLK_ENABLE();

    /* USART6 GPIO Configuration: PC6->TX, PC7->RX */
    GPIO_InitStruct.Pin = GPIO_PIN_6|GPIO_PIN_7;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF8_USART6;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
  }
}

/**
 * @brief 错误处理函数
 */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  System_ErrorHandler();
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  printf("Assert failed: file %s on line %lu\r\n", file, line);
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */

/* USER CODE END 4 */
